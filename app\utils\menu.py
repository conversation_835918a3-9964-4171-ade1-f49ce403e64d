"""
菜单配置和动态生成模块
"""
from flask import url_for
from flask_login import current_user
from datetime import date, timedelta, datetime

# 完整菜单配置
# 每个菜单项包含：
# - id: 唯一标识符
# - name: 显示名称
# - icon: 图标类名（可选）
# - url: 链接地址（可选，对于父菜单可能没有）
# - module 和 action: 对应的权限要求
# - children: 子菜单项（可选）
# - area_level: 区域级别限制（可选，1:县市区, 2:乡镇, 3:学校, 4:食堂）
MENU_CONFIG = [
    {
        'id': 'home',
        'name': '首页',
        'icon': 'fas fa-home',
        'url': 'main.index',
        'children': [
            {
                'id': 'dashboard',
                'name': '仪表盘',
                'url': 'main.index',
                'icon': 'fas fa-tachometer-alt'
            },
            {
                'id': 'daily_management',
                'name': '食堂日常管理',
                'url': 'daily_management.index',
                'icon': 'fas fa-utensils',
                'module': 'daily',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'weekly_menu',
        'name': '周菜单管理',
        'icon': 'fas fa-calendar-alt',
        'module': 'weekly_menu',
        'action': 'view',
        'children': [
            {
                'id': 'weekly_menu_plan',
                'name': '周菜单计划',
                'url': 'weekly_menu_v2.plan',
                'module': 'weekly_menu',
                'action': 'edit'
            },
            {
                'id': 'weekly_menu_list',
                'name': '周菜单列表',
                'url': 'weekly_menu_v2.index',
                'module': 'weekly_menu',
                'action': 'view'
            },
            {
                'id': 'recipe_management',
                'name': '食谱管理',
                'url': 'recipe.index',
                'module': 'recipe',
                'action': 'view'
            },
            {
                'id': 'menu_sync',
                'name': '菜单同步工具',
                'url': 'menu_sync.index',
                'module': 'weekly_menu',
                'action': 'edit'
            }
        ]
    },
    {
        'id': 'purchase',
        'name': '采购管理',
        'icon': 'fas fa-shopping-cart',
        'module': 'purchase',
        'action': 'view',
        'children': [
            {
                'id': 'purchase_order_list',
                'name': '采购订单列表',
                'url': 'purchase_order.index',
                'module': 'purchase',
                'action': 'view'
            },
            {
                'id': 'purchase_order_create',
                'name': '从周菜单创建采购订单',
                'url': 'purchase_order.create_from_menu',
                'module': 'purchase',
                'action': 'create'
            }
        ]
    },
    {
        'id': 'inventory',
        'name': '库存管理',
        'icon': 'fas fa-warehouse',
        'module': 'inventory',
        'action': 'view',
        'children': [
            {
                'id': 'inventory_list',
                'name': '库存列表',
                'url': 'inventory.index',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'stock_in_list',
                'name': '入库管理',
                'url': 'stock_in.index',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'consumption_plan_list',
                'name': '消耗计划管理',
                'url': 'consumption_plan.index',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'stock_out_list',
                'name': '出库管理',
                'url': 'stock_out.index',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'warehouse_list',
                'name': '仓库管理',
                'url': 'warehouse.index',
                'module': 'inventory',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'traceability',
        'name': '食材溯源与留样',
        'icon': 'fas fa-search',
        'module': 'ingredient',
        'action': 'view',
        'children': [
            {
                'id': 'food_trace',
                'name': '食材溯源',
                'url': 'food_trace.index',
                'module': 'ingredient',
                'action': 'view'
            },
            {
                'id': 'food_sample_management',
                'name': '留样管理',
                'url': 'food_trace.sample_management',
                'module': 'food_sample',
                'action': 'view'
            },
            {
                'id': 'food_sample',
                'name': '留样记录',
                'url': 'food_sample.index',
                'module': 'food_sample',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'supplier',
        'name': '供应商管理',
        'icon': 'fas fa-truck',
        'module': 'supplier',
        'action': 'view',
        'children': [
            {
                'id': 'supplier_list',
                'name': '供应商列表',
                'url': 'supplier.index',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'supplier_create',
                'name': '添加供应商',
                'url': 'supplier.create',
                'module': 'supplier',
                'action': 'create'
            },
            {
                'id': 'supplier_category',
                'name': '供应商分类',
                'url': 'supplier_category.index',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'supplier_product_list',
                'name': '供应商产品',
                'url': 'supplier_product.index',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'supplier_school',
                'name': '学校绑定管理',
                'url': 'supplier_school.index',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'product_batch',
                'name': '产品批量上架',
                'url': 'product_batch.index',
                'module': 'supplier',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'employee',
        'name': '员工管理',
        'icon': 'fas fa-users',
        'module': 'user',
        'action': 'view',
        'children': [
            {
                'id': 'employee_list',
                'name': '员工列表',
                'url': 'employee.index',
                'module': 'user',
                'action': 'view'
            },
            {
                'id': 'employee_add',
                'name': '添加员工',
                'url': 'employee.add_employee',
                'module': 'user',
                'action': 'create'
            },
            {
                'id': 'health_certificates',
                'name': '健康证管理',
                'url': 'employee.health_certificates',
                'module': 'user',
                'action': 'view'
            },
            {
                'id': 'daily_health_check',
                'name': '日常健康检查',
                'url': 'employee.daily_health_check',
                'module': 'user',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'area',
        'name': '区域管理',
        'icon': 'fas fa-map-marker-alt',
        'module': 'area',
        'action': 'view',
        'children': [
            {
                'id': 'area_list',
                'name': '区域列表',
                'url': 'area.index',
                'module': 'area',
                'action': 'view'
            },
            {
                'id': 'area_add',
                'name': '添加区域',
                'url': 'area.add_area',
                'module': 'area',
                'action': 'create',
                'admin_only': True
            }
        ]
    },
    {
        'id': 'system',
        'name': '系统管理',
        'icon': 'fas fa-cogs',
        'admin_only': True,
        'children': [
            {
                'id': 'dashboard',
                'name': '管理仪表盘',
                'url': 'system.dashboard',
                'icon': 'fas fa-tachometer-alt',
                'admin_only': True
            },
            {
                'id': 'users',
                'name': '用户管理',
                'url': 'system.users',
                'module': 'user',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'roles',
                'name': '角色管理',
                'url': 'system.roles',
                'module': 'role',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'module_visibility',
                'name': '模块可见性',
                'url': 'system.module_visibility',
                'icon': 'fas fa-eye',
                'admin_only': True
            },
            {
                'id': 'settings',
                'name': '系统设置',
                'url': 'system.settings',
                'icon': 'fas fa-sliders-h',
                'module': 'setting',
                'action': 'view',
                # 移除 admin_only: True
            },
            {
                'id': 'backups',
                'name': '数据备份',
                'url': 'system.backups',
                'icon': 'fas fa-database',
                'module': 'backup',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'monitor',
                'name': '系统监控',
                'url': 'system.settings',
                'icon': 'fas fa-chart-line',
                'module': 'setting',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'security_dashboard',
                'name': '安全仪表盘',
                'url': 'security_admin.dashboard',
                'icon': 'fas fa-shield-alt',
                'module': 'security',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'data_management',
                'name': '系统数据管理',
                'url': 'admin_data.data_management',
                'icon': 'fas fa-database',
                'admin_only': True
            },
            {
                'id': 'system_fix',
                'name': '系统修复工具',
                'url': 'system_fix.index',
                'icon': 'fas fa-tools',
                'admin_only': True
            },
            {
                'id': 'data_repair',
                'name': '数据修复',
                'url': 'data_repair.index',
                'icon': 'fas fa-wrench',
                'module': '数据修复',
                'action': 'view',
                'admin_only': True
            }
        ]
    }
]

def get_url(item):
    """根据菜单项配置生成URL"""
    from datetime import date

    if 'url' not in item:
        return '#'

    # 处理URL参数
    if 'url_params' in item and item['url_params']:
        params = {}
        for key, value in item['url_params'].items():
            # 处理特殊值
            if value == 'today':
                params[key] = date.today().strftime('%Y-%m-%d')
            else:
                params[key] = value
        return url_for(item['url'], **params)

    return url_for(item['url'])

def get_user_menu(user):
    """根据用户权限生成可访问的菜单配置"""
    if not user.is_authenticated:
        return []

    # 导入ModuleVisibility模型
    try:
        from app.models_visibility import ModuleVisibility
    except ImportError:
        # 如果模型不存在，继续使用原有逻辑
        ModuleVisibility = None

    result = []

    for item in MENU_CONFIG:
        # 检查是否仅管理员可见
        if item.get('admin_only') and not user.is_admin():
            continue

        # 检查用户是否有权限访问此菜单项
        if 'module' in item and 'action' in item:
            if not user.has_permission(item['module'], item['action']):
                continue

        # 检查区域级别限制
        if 'area_level' in item and user.area_level > item['area_level']:
            continue

        # 检查模块可见性设置
        if ModuleVisibility and not user.is_admin():  # 超级管理员可以看到所有模块
            # 获取用户的所有角色ID
            role_ids = [role.id for role in user.roles]

            try:
                # 检查每个角色的可见性设置
                module_visible = False
                for role_id in role_ids:
                    if ModuleVisibility.get_visibility(item['id'], role_id):
                        module_visible = True
                        break

                # 如果所有角色都设置为不可见，则跳过此模块
                if not module_visible:
                    continue
            except Exception as e:
                # 出错时默认可见
                from flask import current_app
                if current_app:
                    current_app.logger.error(f"检查模块可见性时出错: {str(e)}")
                # 继续处理此模块

        # 复制菜单项，避免修改原始配置
        menu_item = item.copy()

        # 如果有子菜单，递归过滤
        if 'children' in menu_item and menu_item['children']:
            filtered_children = []
            for child in menu_item['children']:
                # 检查是否仅管理员可见
                if child.get('admin_only') and not user.is_admin():
                    continue

                # 检查用户是否有权限访问此子菜单项
                if 'module' in child and 'action' in child:
                    if not user.has_permission(child['module'], child['action']):
                        continue

                # 检查区域级别限制
                if 'area_level' in child and user.area_level > child['area_level']:
                    continue

                # 检查子模块可见性设置
                if ModuleVisibility and not user.is_admin():
                    try:
                        # 检查每个角色的可见性设置
                        child_visible = False
                        for role_id in role_ids:
                            if ModuleVisibility.get_visibility(child['id'], role_id):
                                child_visible = True
                                break

                        # 如果所有角色都设置为不可见，则跳过此子模块
                        if not child_visible:
                            continue
                    except Exception as e:
                        # 出错时默认可见
                        from flask import current_app
                        if current_app:
                            current_app.logger.error(f"检查子模块可见性时出错: {str(e)}")
                        # 继续处理此子模块

                filtered_children.append(child)

            # 只有当有可访问的子菜单时，才保留此父菜单
            if filtered_children:
                menu_item['children'] = filtered_children
                result.append(menu_item)

        else:
            # 没有子菜单的情况
            result.append(menu_item)

    return result

def get_week_dates(start_date=None):
    """
    获取一周的日期范围

    Args:
        start_date: 开始日期，默认为当前日期

    Returns:
        tuple: (周开始日期, 周结束日期)
    """
    if start_date is None:
        start_date = date.today()
    elif isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    # 获取本周的周一
    monday = start_date - timedelta(days=start_date.weekday())
    # 获取本周的周日
    sunday = monday + timedelta(days=6)

    return monday, sunday

