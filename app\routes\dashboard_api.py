"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from app.services.dashboard_service import DashboardService
from app.models_daily_management import DiningCompanion, Photo, DailyLog
from app.models import Recipe, MenuPlan, MenuRecipe, WeeklyMenu, WeeklyMenuRecipe
from app import db
from sqlalchemy import desc, text
from datetime import datetime, date, timedelta

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    limit = request.args.get('limit', 5, type=int)
    area_id = request.args.get('area_id', type=int)

    # 构建查询
    query = DiningCompanion.query

    # 如果指定了区域，则通过日志筛选区域
    if area_id:
        # 获取指定区域的所有日志ID
        log_ids = db.session.query(DailyLog.id).filter(DailyLog.area_id == area_id).all()
        log_ids = [log_id[0] for log_id in log_ids]

        # 筛选这些日志下的陪餐记录
        if log_ids:
            query = query.filter(DiningCompanion.daily_log_id.in_(log_ids))
        else:
            # 如果没有找到日志，返回空列表
            return jsonify([])

    # 按时间倒序排序并限制数量
    companions = query.order_by(desc(DiningCompanion.dining_time)).limit(limit).all()

    # 构建响应数据
    result = []
    for companion in companions:
        # 获取关联的日志
        log = DailyLog.query.get(companion.daily_log_id) if companion.daily_log_id else None

        # 检查是否有照片
        has_photo = Photo.query.filter_by(
            reference_id=companion.id,
            reference_type='companion'
        ).count() > 0

        # 构建记录数据
        record = {
            'id': companion.id,
            'name': companion.companion_name,
            'role': companion.companion_role,
            'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
            'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
            'meal_type': companion.meal_type,
            'comments': companion.comments,
            'has_photo': has_photo,
            'area_name': log.area.name if log and hasattr(log, 'area') and log.area else '未知区域',
            'taste_rating': companion.taste_rating,
            'hygiene_rating': companion.hygiene_rating,
            'service_rating': companion.service_rating
        }
        result.append(record)

    return jsonify(result)

@dashboard_api_bp.route('/api/v2/dashboard/today-menu', methods=['GET'])
@login_required
def api_v2_today_menu():
    """获取今日菜单 - 专门为首页仪表盘使用"""
    try:
        today_date = date.today()

        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        menu_data = {
            '早餐': {'recipes': [], 'status': '暂无菜单', 'expected_diners': None},
            '午餐': {'recipes': [], 'status': '暂无菜单', 'expected_diners': None},
            '晚餐': {'recipes': [], 'status': '暂无菜单', 'expected_diners': None}
        }

        if area_ids:
            # 直接查询数据库获取今日菜单
            area_ids_str = '(' + ','.join(map(str, area_ids)) + ')'

            # 查询菜单计划
            result = db.session.execute(
                text(f"""
                    SELECT
                        mp.meal_type,
                        mp.status,
                        mp.expected_diners,
                        r.id as recipe_id,
                        r.name as recipe_name,
                        mr.planned_quantity
                    FROM menu_plans mp
                    LEFT JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
                    LEFT JOIN recipes r ON mr.recipe_id = r.id
                    WHERE mp.plan_date = '{today_date.strftime('%Y-%m-%d')}'
                    AND mp.area_id IN {area_ids_str}
                    ORDER BY mp.meal_type, r.name
                """)
            )

            for row in result:
                meal_type = row.meal_type
                if meal_type in menu_data:
                    # 更新状态和预计就餐人数
                    menu_data[meal_type]['status'] = row.status or '计划中'
                    menu_data[meal_type]['expected_diners'] = row.expected_diners

                    # 添加菜谱
                    if row.recipe_id and row.recipe_name:
                        menu_data[meal_type]['recipes'].append({
                            'id': row.recipe_id,
                            'name': row.recipe_name,
                            'quantity': row.planned_quantity
                        })

            # 如果某个餐次没有菜单计划，尝试从周菜单获取
            weekday = today_date.weekday() + 1  # 转换为1-7格式

            for meal_type in menu_data:
                if not menu_data[meal_type]['recipes']:
                    # 查询周菜单
                    weekly_result = db.session.execute(
                        text(f"""
                            SELECT
                                r.id as recipe_id,
                                r.name as recipe_name,
                                wmr.quantity
                            FROM weekly_menus wm
                            INNER JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
                            INNER JOIN recipes r ON wmr.recipe_id = r.id
                            WHERE wm.week_start <= '{today_date.strftime('%Y-%m-%d')}'
                            AND wm.week_end >= '{today_date.strftime('%Y-%m-%d')}'
                            AND wm.area_id IN {area_ids_str}
                            AND wm.status = '已发布'
                            AND wmr.day_of_week = {weekday}
                            AND wmr.meal_type = '{meal_type}'
                            ORDER BY r.name
                        """)
                    )

                    weekly_recipes = []
                    for row in weekly_result:
                        weekly_recipes.append({
                            'id': row.recipe_id,
                            'name': row.recipe_name,
                            'quantity': row.quantity
                        })

                    if weekly_recipes:
                        menu_data[meal_type]['recipes'] = weekly_recipes
                        menu_data[meal_type]['status'] = '计划中'

        return jsonify({
            'success': True,
            'data': menu_data,
            'date': today_date.strftime('%Y-%m-%d')
        })

    except Exception as e:
        current_app.logger.error(f"获取今日菜单失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': {
                '早餐': {'recipes': [], 'status': '获取失败', 'expected_diners': None},
                '午餐': {'recipes': [], 'status': '获取失败', 'expected_diners': None},
                '晚餐': {'recipes': [], 'status': '获取失败', 'expected_diners': None}
            }
        }), 500
